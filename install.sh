#!/bin/bash

# =============================================================================
# 日志清理脚本一键部署工具 - 老王出品
# 功能: 自动部署和配置日志清理脚本
# 作者: 老王 (那个最讨厌SB配置的程序员)
# =============================================================================

set -e  # 遇到错误立即退出，不给SB错误任何机会

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置参数
SCRIPT_NAME="cleanup_logs.sh"
INSTALL_DIR="/usr/local/bin"
LOG_DIR="/JLT/servers/logs"
BACKUP_DIR="/JLT/backup/logs"
CRON_USER="root"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "这个脚本需要root权限运行，艹，用sudo执行！"
        exit 1
    fi
}

# 检查系统类型
check_system() {
    if [[ ! -f /etc/debian_version ]]; then
        log_warn "检测到非Debian系统，某些功能可能不兼容"
    else
        log_info "检测到Debian系统，完美兼容"
    fi
}

# 安装必要的依赖
install_dependencies() {
    log_info "检查并安装必要依赖..."
    
    # 检查并安装gzip
    if ! command -v gzip &> /dev/null; then
        log_info "安装gzip..."
        apt-get update && apt-get install -y gzip
    fi
    
    # 检查并安装cron
    if ! command -v crontab &> /dev/null; then
        log_info "安装cron..."
        apt-get update && apt-get install -y cron
        systemctl enable cron
        systemctl start cron
    fi
    
    log_success "依赖检查完成"
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    directories=("$LOG_DIR" "$BACKUP_DIR" "/var/log")
    
    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            log_info "创建目录: $dir"
            mkdir -p "$dir" || {
                log_error "创建目录失败: $dir"
                exit 1
            }
        else
            log_info "目录已存在: $dir"
        fi
    done
    
    # 设置目录权限
    chmod 755 "$LOG_DIR" "$BACKUP_DIR"
    log_success "目录创建完成"
}

# 部署脚本文件
deploy_script() {
    log_info "部署清理脚本..."
    
    if [[ ! -f "$SCRIPT_NAME" ]]; then
        log_error "找不到脚本文件: $SCRIPT_NAME，艹，文件在哪里？"
        exit 1
    fi
    
    # 复制脚本到安装目录
    cp "$SCRIPT_NAME" "$INSTALL_DIR/" || {
        log_error "复制脚本失败，这个SB系统有问题！"
        exit 1
    }
    
    # 设置执行权限
    chmod +x "$INSTALL_DIR/$SCRIPT_NAME"
    
    log_success "脚本部署完成: $INSTALL_DIR/$SCRIPT_NAME"
}

# 测试脚本功能
test_script() {
    log_info "测试脚本功能..."
    
    # 创建测试日志文件
    test_log="$LOG_DIR/test_$(date +%s).log"
    echo "这是测试日志文件 - $(date)" > "$test_log"
    
    # 执行模拟运行
    log_info "执行模拟运行测试..."
    if "$INSTALL_DIR/$SCRIPT_NAME" --dry-run; then
        log_success "脚本测试通过"
    else
        log_error "脚本测试失败，艹，代码有问题！"
        exit 1
    fi
    
    # 清理测试文件
    rm -f "$test_log"
}

# 配置定时任务
setup_cron() {
    log_info "配置定时任务..."
    
    # 备份现有crontab
    crontab -l > /tmp/crontab_backup_$(date +%s) 2>/dev/null || true
    
    # 检查是否已存在相同的定时任务
    if crontab -l 2>/dev/null | grep -q "$SCRIPT_NAME"; then
        log_warn "检测到已存在的定时任务，跳过添加"
        return
    fi
    
    # 添加定时任务
    (crontab -l 2>/dev/null; echo "# 日志清理任务 - 老王配置") | crontab -
    (crontab -l 2>/dev/null; echo "0 2 * * * $INSTALL_DIR/$SCRIPT_NAME >/dev/null 2>&1") | crontab -
    
    log_success "定时任务配置完成 (每天凌晨2点执行)"
}

# 创建监控脚本
create_monitor() {
    log_info "创建监控脚本..."
    
    monitor_script="/usr/local/bin/monitor_cleanup.sh"
    
    cat > "$monitor_script" << 'EOF'
#!/bin/bash
# 日志清理监控脚本 - 老王出品

LOG_FILE="/var/log/log_cleanup.log"
ALERT_EMAIL="<EMAIL>"  # 修改为实际邮箱

if [[ -f "$LOG_FILE" ]]; then
    # 检查最近24小时的错误
    ERROR_COUNT=$(grep "$(date -d '1 day ago' '+%Y-%m-%d')" "$LOG_FILE" | grep -c "ERROR" || echo "0")
    
    if [[ $ERROR_COUNT -gt 0 ]]; then
        echo "警告: 日志清理脚本在过去24小时内出现 $ERROR_COUNT 个错误"
        echo "请检查日志文件: $LOG_FILE"
        
        # 这里可以添加邮件通知逻辑
        # echo "日志清理错误报告" | mail -s "日志清理告警" "$ALERT_EMAIL"
    fi
else
    echo "警告: 找不到日志清理脚本的运行日志"
fi
EOF
    
    chmod +x "$monitor_script"
    log_success "监控脚本创建完成: $monitor_script"
}

# 显示安装总结
show_summary() {
    log_success "========== 安装完成总结 =========="
    echo -e "${BLUE}脚本位置:${NC} $INSTALL_DIR/$SCRIPT_NAME"
    echo -e "${BLUE}日志目录:${NC} $LOG_DIR"
    echo -e "${BLUE}备份目录:${NC} $BACKUP_DIR"
    echo -e "${BLUE}运行日志:${NC} /var/log/log_cleanup.log"
    echo -e "${BLUE}定时任务:${NC} 每天凌晨2点自动执行"
    echo ""
    echo -e "${YELLOW}常用命令:${NC}"
    echo "  手动执行: $INSTALL_DIR/$SCRIPT_NAME"
    echo "  模拟运行: $INSTALL_DIR/$SCRIPT_NAME --dry-run"
    echo "  查看帮助: $INSTALL_DIR/$SCRIPT_NAME --help"
    echo "  查看日志: tail -f /var/log/log_cleanup.log"
    echo "  查看定时任务: crontab -l"
    echo ""
    log_success "老王的脚本安装完毕，保证比那些SB脚本好用一万倍！"
}

# 卸载函数
uninstall() {
    log_info "开始卸载日志清理脚本..."
    
    # 删除脚本文件
    rm -f "$INSTALL_DIR/$SCRIPT_NAME"
    rm -f "/usr/local/bin/monitor_cleanup.sh"
    
    # 删除定时任务
    crontab -l 2>/dev/null | grep -v "$SCRIPT_NAME" | crontab -
    
    # 询问是否删除日志和备份目录
    read -p "是否删除日志和备份目录? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -rf "$BACKUP_DIR"
        log_info "已删除备份目录"
    fi
    
    log_success "卸载完成"
}

# 主函数
main() {
    echo -e "${GREEN}"
    echo "=========================================="
    echo "    日志清理脚本一键部署工具"
    echo "    老王出品 - 专治各种SB配置"
    echo "=========================================="
    echo -e "${NC}"
    
    case "${1:-install}" in
        "install")
            check_root
            check_system
            install_dependencies
            create_directories
            deploy_script
            test_script
            setup_cron
            create_monitor
            show_summary
            ;;
        "uninstall")
            check_root
            uninstall
            ;;
        "test")
            test_script
            ;;
        *)
            echo "用法: $0 [install|uninstall|test]"
            echo "  install   - 安装脚本 (默认)"
            echo "  uninstall - 卸载脚本"
            echo "  test      - 测试脚本"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
