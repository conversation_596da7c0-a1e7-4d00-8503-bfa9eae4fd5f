@echo off
chcp 65001 >nul
echo ========================================
echo 微信公众号图片获取工具 - 快速启动
echo ========================================
echo.

echo 请选择操作：
echo.
echo 1. 一键下载并生成PDF（推荐）
echo 2. 仅下载图片
echo 3. 仅生成PDF（需要已有图片）
echo 4. 验证图片顺序
echo 5. 退出
echo.

set /p choice=请输入选项数字 (1-5): 

if "%choice%"=="1" goto one_click
if "%choice%"=="2" goto download_only
if "%choice%"=="3" goto pdf_only
if "%choice%"=="4" goto verify_order
if "%choice%"=="5" goto exit
goto invalid_choice

:one_click
echo.
set /p url=请输入微信公众号文章URL: 
if "%url%"=="" (
    echo ❌ URL不能为空
    pause
    goto end
)
echo.
echo 正在执行一键下载并生成PDF...
python "一键下载并生成PDF.py" "%url%"
goto end

:download_only
echo.
set /p url=请输入微信公众号文章URL: 
if "%url%"=="" (
    echo ❌ URL不能为空
    pause
    goto end
)
echo.
echo 正在下载图片...
python simple_image_downloader.py "%url%"
goto end

:pdf_only
echo.
echo 正在生成PDF...
python create_pdf.py
goto end

:verify_order
echo.
echo 正在验证图片顺序...
python verify_order.py
goto end

:invalid_choice
echo.
echo ❌ 无效选项，请重新选择
pause
goto start

:exit
echo.
echo 👋 再见！
timeout /t 2 >nul
exit /b 0

:end
echo.
echo ========================================
echo ✅ 操作完成！
echo ========================================
pause
