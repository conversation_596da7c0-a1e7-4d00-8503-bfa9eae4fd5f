@echo off
chcp 65001 >nul
echo ========================================
echo 微信公众号图片获取工具 - 依赖安装
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python环境
    echo 请先安装Python 3.7或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 正在安装依赖库...
echo.

pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo ❌ 依赖安装失败
    echo 请检查网络连接或手动执行：pip install -r requirements.txt
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ 依赖安装完成！
echo ========================================
echo.
echo 现在你可以使用以下命令：
echo.
echo 1. 一键下载并生成PDF：
echo    python 一键下载并生成PDF.py "你的URL"
echo.
echo 2. 分步操作：
echo    python wechat_image_downloader.py
echo    python create_pdf.py
echo.
echo 3. 查看帮助：
echo    查看 README.md 或 完整使用说明.md
echo.
pause
