#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信公众号文章图片下载器
从指定的微信公众号文章URL中提取并下载所有图片
"""

import requests
import os
import re
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import time
from pathlib import Path


class WeChatImageDownloader:
    def __init__(self, output_dir="images"):
        """
        初始化下载器
        
        Args:
            output_dir (str): 图片保存目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 设置请求头，模拟浏览器访问
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def get_page_content(self, url):
        """
        获取网页内容
        
        Args:
            url (str): 网页URL
            
        Returns:
            str: 网页HTML内容
        """
        try:
            print(f"正在获取页面内容: {url}")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            return response.text
        except requests.RequestException as e:
            print(f"获取页面内容失败: {e}")
            return None
    
    def extract_image_urls(self, html_content, base_url):
        """
        从HTML内容中提取图片URL

        Args:
            html_content (str): HTML内容
            base_url (str): 基础URL，用于处理相对路径

        Returns:
            list: 图片URL列表
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        image_urls = []
        seen_urls = set()  # 用于去重，但保持顺序

        # 优先查找文章内容区域
        content_areas = [
            soup.find('div', {'id': 'js_content'}),  # 微信文章主要内容区域
            soup.find('div', class_='rich_media_content'),  # 富媒体内容区域
            soup.find('section', class_='article-content'),  # 其他可能的内容区域
            soup  # 如果找不到特定区域，则搜索整个页面
        ]

        # 使用第一个找到的内容区域
        content_area = None
        for area in content_areas:
            if area:
                content_area = area
                break

        if not content_area:
            content_area = soup

        print(f"在内容区域中查找图片...")

        # 在内容区域中查找所有img标签，按照在HTML中出现的顺序
        img_tags = content_area.find_all('img')

        for img in img_tags:
            # 获取图片URL，优先使用data-src（懒加载），然后是src
            img_url = img.get('data-src') or img.get('src')

            if img_url:
                # 处理相对路径
                if img_url.startswith('//'):
                    img_url = 'https:' + img_url
                elif img_url.startswith('/'):
                    img_url = urljoin(base_url, img_url)
                elif not img_url.startswith(('http://', 'https://')):
                    img_url = urljoin(base_url, img_url)

                # 过滤掉一些不需要的图片（如表情包、小图标等）
                if self.is_valid_image_url(img_url) and img_url not in seen_urls:
                    image_urls.append(img_url)
                    seen_urls.add(img_url)

        print(f"找到 {len(image_urls)} 张图片（按文章顺序）")

        return image_urls
    
    def is_valid_image_url(self, url):
        """
        判断是否为有效的图片URL
        
        Args:
            url (str): 图片URL
            
        Returns:
            bool: 是否有效
        """
        # 过滤掉一些明显不是内容图片的URL
        invalid_patterns = [
            r'emoji',  # 表情包
            r'icon',   # 图标
            r'avatar', # 头像
            r'logo',   # logo
            r'qrcode', # 二维码
            r'\.gif$', # gif动图（可选择保留）
        ]
        
        for pattern in invalid_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return False
        
        # 检查是否为图片格式
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        parsed_url = urlparse(url)
        path = parsed_url.path.lower()
        
        # 如果URL包含图片扩展名，或者是微信的图片服务器
        if any(path.endswith(ext) for ext in image_extensions) or 'mmbiz.qpic.cn' in url:
            return True
        
        return False
    
    def download_image(self, img_url, filename):
        """
        下载单张图片
        
        Args:
            img_url (str): 图片URL
            filename (str): 保存的文件名
            
        Returns:
            bool: 下载是否成功
        """
        try:
            print(f"正在下载: {filename}")
            response = self.session.get(img_url, timeout=30)
            response.raise_for_status()
            
            filepath = self.output_dir / filename
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            print(f"下载成功: {filepath}")
            return True
            
        except requests.RequestException as e:
            print(f"下载失败 {filename}: {e}")
            return False
    
    def generate_filename(self, img_url, index):
        """
        生成文件名
        
        Args:
            img_url (str): 图片URL
            index (int): 图片索引
            
        Returns:
            str: 文件名
        """
        parsed_url = urlparse(img_url)
        path = parsed_url.path
        
        # 尝试从URL中提取文件扩展名
        if '.' in path:
            ext = path.split('.')[-1].lower()
            if ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']:
                return f"image_{index:03d}.{ext}"
        
        # 默认使用jpg扩展名
        return f"image_{index:03d}.jpg"
    
    def download_all_images(self, url):
        """
        下载指定URL页面的所有图片
        
        Args:
            url (str): 页面URL
            
        Returns:
            tuple: (成功下载数量, 总图片数量)
        """
        # 获取页面内容
        html_content = self.get_page_content(url)
        if not html_content:
            return 0, 0
        
        # 提取图片URL
        image_urls = self.extract_image_urls(html_content, url)
        if not image_urls:
            print("未找到任何图片")
            return 0, 0
        
        # 下载图片
        success_count = 0
        total_count = len(image_urls)
        
        for i, img_url in enumerate(image_urls, 1):
            filename = self.generate_filename(img_url, i)
            
            if self.download_image(img_url, filename):
                success_count += 1
            
            # 添加延时，避免请求过于频繁
            time.sleep(1)
        
        print(f"\n下载完成！成功下载 {success_count}/{total_count} 张图片")
        print(f"图片保存在: {self.output_dir.absolute()}")
        
        return success_count, total_count


def main():
    """主函数"""
    # 目标URL
    url = "https://mp.weixin.qq.com/s/DexacUPuNAq5blOIRGhTXQ?scene=1&poc_token=HCIHuWijIxNKh90wh5oFWjHNZTJtXhYjJBsBiE2s"
    
    # 创建下载器实例
    downloader = WeChatImageDownloader(output_dir="wechat_images1")
    
    # 下载所有图片
    success, total = downloader.download_all_images(url)
    
    if total > 0:
        print(f"\n任务完成！成功率: {success/total*100:.1f}%")
    else:
        print("\n未找到任何图片或页面访问失败")


if __name__ == "__main__":
    main()
