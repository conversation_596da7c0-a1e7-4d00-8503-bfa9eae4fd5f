# 使用流程示例

## 🚀 完整使用流程演示

### 方法一：一键完成（推荐新手）

1. **安装依赖**
   ```bash
   # Windows用户：双击运行"安装依赖.bat"
   # 或手动执行：
   pip install -r requirements.txt
   ```

2. **一键下载并生成PDF**
   ```bash
   # Windows用户：双击运行"快速启动.bat"，选择选项1
   # 或手动执行：
   python 一键下载并生成PDF.py "https://mp.weixin.qq.com/s/your-article-url"
   ```

3. **查看结果**
   - 图片保存在：`wechat_images/` 文件夹
   - PDF文件保存在：当前目录，文件名如 `wechat_images_20240904_105104.pdf`

### 方法二：分步操作

1. **第一步：下载图片**
   ```bash
   python wechat_image_downloader.py
   ```
   - 会在当前目录创建 `wechat_images` 文件夹
   - 图片按顺序命名：image_001.jpg, image_002.jpg, ...

2. **第二步：生成PDF**
   ```bash
   python create_pdf.py
   ```
   - 读取 `wechat_images` 文件夹中的图片
   - 生成带时间戳的PDF文件

### 方法三：自定义操作

1. **下载指定URL的图片**
   ```bash
   python simple_image_downloader.py "https://mp.weixin.qq.com/s/your-url"
   ```

2. **预览图片顺序**
   ```bash
   python verify_order.py
   ```

3. **自定义PDF生成**
   ```bash
   # 指定输出文件名
   python images_to_pdf.py wechat_images -o "我的文章.pdf"
   
   # 保持原始图片大小
   python images_to_pdf.py wechat_images --no-resize
   ```

## 📁 文件夹结构示例

### 使用前
```
微信公众号图片获取/
├── README.md
├── requirements.txt
├── wechat_image_downloader.py
├── create_pdf.py
└── ... (其他文件)
```

### 使用后
```
微信公众号图片获取/
├── README.md
├── requirements.txt
├── wechat_image_downloader.py
├── create_pdf.py
├── ... (其他文件)
├── wechat_images/                 # 自动创建的图片文件夹
│   ├── image_001.jpg
│   ├── image_002.jpg
│   ├── image_003.jpg
│   └── ...
└── wechat_images_20240904_105104.pdf  # 生成的PDF文件
```

## 🎯 实际使用示例

### 示例1：下载技术教程
```bash
# 下载一篇Python教程文章
python 一键下载并生成PDF.py "https://mp.weixin.qq.com/s/python-tutorial-url"

# 结果：
# - 下载了25张图片到wechat_images文件夹
# - 生成了python_tutorial_20240904_105104.pdf文件
```

### 示例2：批量处理多篇文章
```bash
# 下载第一篇文章
python simple_image_downloader.py "https://mp.weixin.qq.com/s/article1"

# 下载第二篇文章到不同文件夹
python simple_image_downloader.py "https://mp.weixin.qq.com/s/article2"

# 分别生成PDF
python images_to_pdf.py downloaded_images -o "文章合集.pdf"
```

### 示例3：高质量保存
```bash
# 下载图片
python wechat_image_downloader.py

# 生成高质量PDF（不缩放图片）
python images_to_pdf.py wechat_images --no-resize -o "高清版本.pdf"
```

## ⚠️ 常见问题解决

### 问题1：找不到wechat_images文件夹
**原因**：还没有运行过图片下载程序
**解决**：先运行 `python wechat_image_downloader.py` 下载图片

### 问题2：PDF生成失败
**原因**：图片文件夹为空或图片损坏
**解决**：检查wechat_images文件夹是否包含图片文件

### 问题3：图片下载失败
**原因**：网络问题或URL无效
**解决**：检查网络连接，确认URL格式正确

### 问题4：依赖库缺失
**原因**：没有安装必要的Python库
**解决**：运行 `pip install -r requirements.txt`

## 💡 使用技巧

1. **预览功能**：使用 `verify_order.py` 可以预览图片顺序，确认无误后再下载
2. **自定义文件名**：使用 `-o` 参数可以自定义PDF文件名
3. **批处理工具**：Windows用户可以使用 `.bat` 文件快速启动
4. **文件管理**：建议为不同的文章创建不同的工作文件夹

## 🎉 成功标志

当你看到以下输出时，说明操作成功：

```
✅ PDF创建成功!
📁 输出文件: C:\...\wechat_images_20240904_105104.pdf
📊 统计信息:
   - 总图片数: 71
   - 成功处理: 71
   - 处理失败: 0
   - 文件大小: 7.5 MB

🎉 任务完成！
```
