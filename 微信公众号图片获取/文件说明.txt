微信公众号图片获取工具 - 文件说明
========================================

📁 核心程序文件：
├── wechat_image_downloader.py     # 主要的图片下载器，包含完整功能
├── simple_image_downloader.py     # 简化版图片下载器，支持命令行参数
├── verify_order.py                # 图片顺序验证工具，可预览下载顺序
├── images_to_pdf.py               # 完整功能的PDF生成器，支持多种参数
├── create_pdf.py                  # 简化版PDF生成器，一键生成PDF
└── 一键下载并生成PDF.py           # 集成工具，从URL到PDF一步完成

📋 配置文件：
└── requirements.txt               # Python依赖库列表

📖 说明文档：
├── README.md                      # 项目总体介绍和快速开始指南
├── 完整使用说明.md                # 详细的使用教程和高级功能
├── 图片下载器使用说明.md          # 图片下载功能的专门说明
├── PDF合成工具使用说明.md         # PDF生成功能的专门说明
└── 文件说明.txt                   # 本文件，各文件作用说明

🚀 快速启动工具：
├── 安装依赖.bat                   # Windows批处理，一键安装Python依赖
└── 快速启动.bat                   # Windows批处理，图形化菜单启动工具

========================================

🎯 新手推荐使用顺序：
1. 双击运行"安装依赖.bat"安装依赖库
2. 双击运行"快速启动.bat"选择功能
3. 或直接使用命令：python 一键下载并生成PDF.py "你的URL"

🔧 高级用户：
- 查看"完整使用说明.md"了解所有功能
- 根据需要选择合适的工具文件
- 可以修改源代码自定义功能

📞 需要帮助？
- 查看README.md获取快速指导
- 查看完整使用说明.md获取详细教程
- 检查各个专门的说明文档
