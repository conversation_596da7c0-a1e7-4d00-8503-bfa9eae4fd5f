#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证图片下载顺序的脚本
显示提取到的图片URL列表，不实际下载
"""

from wechat_image_downloader import WeChatImageDownloader


def main():
    """主函数 - 仅显示图片URL顺序，不下载"""
    url = "https://mp.weixin.qq.com/s/tQUwoRwZ1wohQ4HOsn1BLg?scene=1"
    
    # 创建下载器实例
    downloader = WeChatImageDownloader()
    
    # 获取页面内容
    html_content = downloader.get_page_content(url)
    if not html_content:
        print("无法获取页面内容")
        return
    
    # 提取图片URL
    image_urls = downloader.extract_image_urls(html_content, url)
    
    if not image_urls:
        print("未找到任何图片")
        return
    
    print(f"\n按文章顺序找到的图片URL列表：")
    print("=" * 80)
    
    for i, img_url in enumerate(image_urls, 1):
        print(f"{i:3d}. {img_url}")
    
    print("=" * 80)
    print(f"总计：{len(image_urls)} 张图片")
    
    # 询问是否要下载
    choice = input("\n是否要按此顺序下载所有图片？(y/n): ").strip().lower()
    if choice == 'y':
        success, total = downloader.download_all_images(url)
        print(f"\n下载完成！成功下载 {success}/{total} 张图片")


if __name__ == "__main__":
    main()
