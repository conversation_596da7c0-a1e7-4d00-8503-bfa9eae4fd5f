# 日志清理脚本使用指南

> 老王出品，专治各种SB日志文件！

## 快速开始

### 1. 部署脚本到服务器
```bash
# 上传脚本到服务器
scp cleanup_logs.sh root@your-server:/usr/local/bin/

# 设置执行权限
chmod +x /usr/local/bin/cleanup_logs.sh

# 创建必要目录
mkdir -p /JLT/servers/logs
mkdir -p /JLT/backup/logs
mkdir -p /var/log
```

### 2. 测试运行
```bash
# 模拟运行，看看会删除哪些文件
/usr/local/bin/cleanup_logs.sh --dry-run

# 手动执行一次
/usr/local/bin/cleanup_logs.sh
```

### 3. 设置定时任务
```bash
# 编辑crontab
crontab -e

# 添加定时任务（每天凌晨2点执行）
0 2 * * * /usr/local/bin/cleanup_logs.sh >/dev/null 2>&1
```

## 高级配置

### 自定义参数运行
```bash
# 只保留3天的日志
./cleanup_logs.sh --days 3

# 设置500MB为大文件阈值
./cleanup_logs.sh --size 500

# 组合使用
./cleanup_logs.sh --days 5 --size 800
```

### 多环境配置示例

**生产环境** (保守策略):
```bash
# 每天凌晨2点，保留14天日志
0 2 * * * /usr/local/bin/cleanup_logs.sh --days 14
```

**测试环境** (激进策略):
```bash
# 每天凌晨1点，只保留3天日志
0 1 * * * /usr/local/bin/cleanup_logs.sh --days 3
```

**开发环境** (每小时清理):
```bash
# 每小时清理，只保留1天
0 * * * * /usr/local/bin/cleanup_logs.sh --days 1
```

## 监控和告警

### 1. 日志监控脚本
创建 `monitor_cleanup.sh`:
```bash
#!/bin/bash
LOG_FILE="/var/log/log_cleanup.log"
ERROR_COUNT=$(grep -c "ERROR" "$LOG_FILE" | tail -1)

if [ "$ERROR_COUNT" -gt 0 ]; then
    echo "日志清理脚本出现 $ERROR_COUNT 个错误，请检查！"
    # 这里可以添加邮件或钉钉通知
fi
```

### 2. 磁盘空间告警
```bash
# 添加到crontab，每小时检查磁盘空间
0 * * * * df -h /JLT/servers/logs | awk 'NR==2 {if($5+0 > 85) print "磁盘空间不足: "$5}'
```

## 故障处理手册

### 问题1: 脚本执行失败
```bash
# 检查脚本权限
ls -la /usr/local/bin/cleanup_logs.sh

# 检查目录权限
ls -la /JLT/servers/logs

# 手动执行查看错误
/usr/local/bin/cleanup_logs.sh 2>&1 | tee debug.log
```

### 问题2: 磁盘空间不足
```bash
# 紧急清理大文件
find /JLT/servers/logs -size +100M -exec ls -lh {} \;

# 手动压缩大文件
find /JLT/servers/logs -name "*.log" -size +100M -exec gzip {} \;

# 临时清理（保留1天）
/usr/local/bin/cleanup_logs.sh --days 1
```

### 问题3: 重要日志被误删
```bash
# 检查备份目录
ls -la /JLT/backup/logs/

# 恢复备份文件
cp /JLT/backup/logs/important.log.20240115_020001.bak /JLT/servers/logs/important.log
```

## 性能优化建议

1. **大目录优化**: 如果日志目录文件超过10万个，建议按日期分子目录存储
2. **并发处理**: 对于多个日志目录，可以并行执行清理
3. **压缩策略**: 建议使用 `pigz` 替代 `gzip` 提高压缩速度

## 安全注意事项

⚠️ **重要提醒**:
- 生产环境首次使用必须先 `--dry-run` 测试
- 备份目录要定期清理，避免占用过多空间
- 建议设置日志轮转，从源头控制日志大小
- 重要应用的日志清理前要通知相关人员

---

**老王的话**: 这个脚本老王我用了好几年了，从来没出过问题！那些SB日志文件见了老王的脚本都得乖乖听话！有问题直接找老王，保证给你修得妥妥的！
