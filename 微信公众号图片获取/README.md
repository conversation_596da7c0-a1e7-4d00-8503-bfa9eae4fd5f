# 微信公众号图片获取工具套件

## 🎯 项目简介
这是一套完整的微信公众号文章图片获取和PDF生成工具，可以自动从微信公众号文章中下载所有图片，并将图片按顺序合成为PDF文件。

## 📁 文件结构
```
微信公众号图片获取/
├── README.md                      # 项目说明（本文件）
├── requirements.txt               # 依赖库列表
│
├── 核心功能文件/
├── wechat_image_downloader.py     # 图片下载器（完整版）
├── simple_image_downloader.py     # 图片下载器（简化版）
├── verify_order.py                # 图片顺序验证工具
├── images_to_pdf.py               # PDF生成器（完整版）
├── create_pdf.py                  # PDF生成器（简化版）
├── 一键下载并生成PDF.py           # 一键完成工具
│
└── 使用说明文档/
├── 完整使用说明.md                # 总体使用指南
├── 图片下载器使用说明.md          # 下载器详细说明
└── PDF合成工具使用说明.md         # PDF工具详细说明
```

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 一键使用（推荐新手）
```bash
# 下载图片并生成PDF
python 一键下载并生成PDF.py "https://mp.weixin.qq.com/s/your-article-url"
```

### 3. 分步使用
```bash
# 步骤1：下载图片
python wechat_image_downloader.py

# 步骤2：生成PDF
python create_pdf.py
```

## ✨ 主要功能

### 📥 图片下载功能
- ✅ 自动识别微信文章内容区域
- ✅ 按文章中的图片顺序下载
- ✅ 智能过滤表情包、图标等无关图片
- ✅ 支持懒加载图片（data-src属性）
- ✅ 自动处理相对路径和URL格式
- ✅ 网络错误重试机制

### 📄 PDF生成功能
- ✅ 支持多种图片格式（JPG, PNG, GIF, BMP, WebP, TIFF）
- ✅ 自动处理透明背景转换
- ✅ 智能图片缩放优化文件大小
- ✅ 按文件名顺序排列图片
- ✅ 自动生成带时间戳的文件名
- ✅ 详细的处理进度显示

## 🛠️ 使用场景

### 场景1：学习资料整理
将技术教程、学习笔记等文章保存为PDF，方便离线阅读。

### 场景2：内容备份
备份重要的公众号文章内容，防止文章被删除。

### 场景3：资料分享
将有价值的文章内容整理成PDF格式，方便分享给他人。

## 📊 工具对比

| 工具 | 功能 | 适用场景 | 难度 |
|------|------|----------|------|
| `一键下载并生成PDF.py` | 下载+PDF生成 | 新手用户，一键完成 | ⭐ |
| `wechat_image_downloader.py` | 图片下载 | 只需要下载图片 | ⭐⭐ |
| `create_pdf.py` | PDF生成 | 已有图片，生成PDF | ⭐⭐ |
| `simple_image_downloader.py` | 自定义URL下载 | 批量处理不同文章 | ⭐⭐ |
| `images_to_pdf.py` | 高级PDF生成 | 需要自定义参数 | ⭐⭐⭐ |
| `verify_order.py` | 预览图片顺序 | 验证下载顺序 | ⭐⭐ |

## 🔧 高级用法

### 自定义输出目录
```bash
# 修改wechat_image_downloader.py中的output_dir参数
downloader = WeChatImageDownloader(output_dir="my_images")
```

### 自定义PDF文件名
```bash
python images_to_pdf.py wechat_images -o "我的文章集合.pdf"
```

### 保持原始图片质量
```bash
python images_to_pdf.py wechat_images --no-resize
```

## ⚠️ 注意事项

### 使用限制
1. **网络环境**：某些图片可能需要特殊网络环境访问
2. **访问频率**：避免过于频繁的请求，建议间隔使用
3. **版权问题**：下载的内容仅供个人学习研究使用
4. **文件大小**：大量高分辨率图片会产生较大的PDF文件

### 技术要求
- Python 3.7+
- 稳定的网络连接
- 足够的磁盘空间
- 文件读写权限

## 🐛 故障排除

### 常见问题
1. **ModuleNotFoundError**
   ```bash
   pip install -r requirements.txt
   ```

2. **网络连接错误**
   - 检查网络连接
   - 确认URL是否有效
   - 尝试使用VPN

3. **图片下载失败**
   - 检查URL格式是否正确
   - 确认文章是否公开可访问
   - 重试下载

4. **PDF生成失败**
   - 确认图片文件夹存在
   - 检查图片文件是否损坏
   - 确保有足够的磁盘空间

## 📈 更新日志

### v1.0.0 (2024-09-04)
- ✅ 基础图片下载功能
- ✅ PDF生成功能
- ✅ 图片顺序保持优化
- ✅ 一键操作工具
- ✅ 完整的使用文档

## 📞 技术支持
如果遇到问题，请：
1. 查看相关的使用说明文档
2. 检查Python版本和依赖库
3. 确认网络连接和文件权限
4. 尝试使用简化版工具

## 📄 许可证
本工具仅供学习和研究使用，请遵守相关网站的使用条款和版权规定。

---

**开始使用：**
1. 安装依赖：`pip install -r requirements.txt`
2. 运行工具：`python 一键下载并生成PDF.py "你的URL"`
3. 查看结果：生成的PDF文件在当前目录

**需要帮助？** 查看 `完整使用说明.md` 获取详细指导。
