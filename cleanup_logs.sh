#!/bin/bash

# =============================================================================
# 日志清理脚本 - 老王出品，必属精品
# 功能: 定时清理 /JLT/servers/logs 目录下的日志文件
# 作者: 老王 (那个见不得代码报错的暴躁程序员)
# 创建时间: $(date +"%Y-%m-%d %H:%M:%S")
# =============================================================================

# 配置参数 - 别tm乱改这些参数，老王我调试了半天的
LOG_DIR="/JLT/servers/logs"                    # 日志目录路径
KEEP_DAYS=7                                    # 保留最近几天的日志，默认7天
MAX_SIZE_MB=1000                               # 单个日志文件最大大小(MB)，超过就压缩
BACKUP_DIR="/JLT/backup/logs"                  # 备份目录
SCRIPT_LOG="/var/log/log_cleanup.log"          # 脚本运行日志

# 颜色定义 - 让输出好看点，不然看着憋屈
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数 - 规范化输出，不像那些SB脚本到处echo
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$SCRIPT_LOG"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$SCRIPT_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$SCRIPT_LOG"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$SCRIPT_LOG"
}

# 检查目录是否存在的函数 - 基础检查，避免SB错误
check_directory() {
    local dir="$1"
    if [[ ! -d "$dir" ]]; then
        log_error "目录 $dir 不存在，艹，这个憨批路径是谁配置的？"
        return 1
    fi
    return 0
}

# 创建备份目录
create_backup_dir() {
    if [[ ! -d "$BACKUP_DIR" ]]; then
        log_info "创建备份目录: $BACKUP_DIR"
        mkdir -p "$BACKUP_DIR" || {
            log_error "创建备份目录失败，这个SB系统权限有问题！"
            exit 1
        }
    fi
}

# 压缩大文件函数 - 那些憨批日志文件动不动几个G
compress_large_files() {
    log_info "开始检查并压缩大文件..."
    
    find "$LOG_DIR" -type f -name "*.log" -size +${MAX_SIZE_MB}M | while read -r file; do
        if [[ -f "$file" ]]; then
            log_info "发现大文件: $file ($(du -h "$file" | cut -f1))"
            
            # 备份原文件
            backup_file="$BACKUP_DIR/$(basename "$file").$(date +%Y%m%d_%H%M%S).bak"
            cp "$file" "$backup_file" && log_info "备份文件到: $backup_file"
            
            # 压缩文件
            gzip "$file" && {
                log_info "成功压缩文件: $file -> $file.gz"
            } || {
                log_error "压缩文件失败: $file，这个SB文件可能被占用了"
            }
        fi
    done
}

# 清理过期日志函数 - 核心功能，删除老旧日志
cleanup_old_logs() {
    log_info "开始清理 $KEEP_DAYS 天前的日志文件..."
    
    # 统计清理前的文件数量和大小
    before_count=$(find "$LOG_DIR" -type f | wc -l)
    before_size=$(du -sh "$LOG_DIR" 2>/dev/null | cut -f1)
    
    log_info "清理前统计: 文件数量=$before_count, 目录大小=$before_size"
    
    # 查找并删除过期文件
    deleted_count=0
    find "$LOG_DIR" -type f -mtime +$KEEP_DAYS | while read -r file; do
        if [[ -f "$file" ]]; then
            file_size=$(du -h "$file" | cut -f1)
            log_debug "删除过期文件: $file (大小: $file_size)"
            
            # 删除文件
            rm -f "$file" && {
                ((deleted_count++))
                log_info "成功删除: $file"
            } || {
                log_error "删除失败: $file，这个憨批文件删不掉"
            }
        fi
    done
    
    # 统计清理后的结果
    after_count=$(find "$LOG_DIR" -type f | wc -l)
    after_size=$(du -sh "$LOG_DIR" 2>/dev/null | cut -f1)
    
    log_info "清理完成! 清理后统计: 文件数量=$after_count, 目录大小=$after_size"
    log_info "本次清理删除了 $((before_count - after_count)) 个文件"
}

# 清理空目录函数 - 删除空的子目录，保持目录结构整洁
cleanup_empty_dirs() {
    log_info "清理空目录..."
    
    find "$LOG_DIR" -type d -empty | while read -r dir; do
        if [[ "$dir" != "$LOG_DIR" ]]; then
            log_debug "删除空目录: $dir"
            rmdir "$dir" 2>/dev/null && log_info "成功删除空目录: $dir"
        fi
    done
}

# 磁盘空间检查函数 - 防止磁盘爆满
check_disk_space() {
    log_info "检查磁盘空间..."
    
    disk_usage=$(df "$LOG_DIR" | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [[ $disk_usage -gt 90 ]]; then
        log_warn "磁盘使用率过高: ${disk_usage}%，艹，这个SB系统快爆了！"
        # 可以在这里添加告警逻辑，比如发邮件或者钉钉通知
    elif [[ $disk_usage -gt 80 ]]; then
        log_warn "磁盘使用率较高: ${disk_usage}%，需要关注"
    else
        log_info "磁盘使用率正常: ${disk_usage}%"
    fi
}

# 主函数 - 脚本入口点
main() {
    log_info "========== 日志清理脚本开始执行 =========="
    log_info "配置参数: 日志目录=$LOG_DIR, 保留天数=$KEEP_DAYS, 最大文件大小=${MAX_SIZE_MB}MB"
    
    # 检查必要目录
    check_directory "$LOG_DIR" || exit 1
    
    # 创建备份目录
    create_backup_dir
    
    # 检查磁盘空间
    check_disk_space
    
    # 压缩大文件
    compress_large_files
    
    # 清理过期日志
    cleanup_old_logs
    
    # 清理空目录
    cleanup_empty_dirs
    
    # 最终检查
    check_disk_space
    
    log_info "========== 日志清理脚本执行完成 =========="
}

# 脚本执行入口 - 支持命令行参数
case "${1:-}" in
    --help|-h)
        echo "用法: $0 [选项]"
        echo "选项:"
        echo "  --help, -h     显示帮助信息"
        echo "  --dry-run      模拟运行，不实际删除文件"
        echo "  --days N       指定保留天数 (默认: $KEEP_DAYS)"
        echo "  --size N       指定最大文件大小MB (默认: $MAX_SIZE_MB)"
        echo ""
        echo "示例:"
        echo "  $0                    # 使用默认配置运行"
        echo "  $0 --days 3           # 只保留3天的日志"
        echo "  $0 --dry-run          # 模拟运行"
        exit 0
        ;;
    --dry-run)
        log_info "模拟运行模式 - 不会实际删除文件"
        # 这里可以添加模拟运行的逻辑
        exit 0
        ;;
    --days)
        KEEP_DAYS="$2"
        log_info "使用自定义保留天数: $KEEP_DAYS"
        ;;
    --size)
        MAX_SIZE_MB="$2"
        log_info "使用自定义最大文件大小: ${MAX_SIZE_MB}MB"
        ;;
esac

# 执行主函数
main

# 脚本结束
exit 0
